2025-03-20 08:59:35,056 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"ACC-SINV-2025-00002","owner":"Administrator","creation":"2025-03-20 08:58:14.186446","modified":"2025-03-20 08:58:14.186446","modified_by":"Administrator","docstatus":0,"idx":0,"title":"Kiboko ltd","naming_series":"ACC-SINV-.YYYY.-","customer":"Kiboko ltd","customer_name":"Kiboko ltd","tax_id":null,"company":"Design System","company_tax_id":null,"posting_date":"2025-03-20","posting_time":"08:58:14.337189","set_posting_time":0,"due_date":"2025-03-20","is_pos":0,"pos_profile":null,"is_consolidated":0,"is_return":0,"previous_invoice_number":null,"return_against":null,"update_outstanding_for_self":1,"update_billed_amount_in_sales_order":0,"update_billed_amount_in_delivery_note":1,"is_debit_note":0,"amended_from":null,"authotp_method":null,"authotp_validated":0,"cost_center":null,"project":null,"currency":"TZS","conversion_rate":1,"selling_price_list":"Standard Selling","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"scan_barcode":null,"update_stock":0,"custom_is_trade_in":0,"default_item_discount":0,"default_item_tax_template":null,"set_warehouse":null,"set_target_warehouse":null,"total_qty":3,"total_net_weight":0,"base_total":1350000,"base_net_total":1350000,"price_reduction":0,"total":1350000,"net_total":1350000,"tax_category":"","taxes_and_charges":null,"shipping_rule":null,"incoterm":null,"named_place":null,"base_total_taxes_and_charges":0,"total_taxes_and_charges":0,"base_grand_total":1350000,"base_rounding_adjustment":0,"base_rounded_total":1350000,"base_in_words":"","grand_total":1350000,"rounding_adjustment":0,"use_company_roundoff_cost_center":0,"rounded_total":1350000,"in_words":"","total_advance":0,"outstanding_amount":1350000,"disable_rounded_total":0,"apply_discount_on":"Grand Total","base_discount_amount":0,"is_cash_or_non_trade_discount":0,"additional_discount_account":null,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":null,"total_billing_hours":0,"total_billing_amount":0,"cash_bank_account":null,"base_paid_amount":0,"paid_amount":0,"base_change_amount":0,"change_amount":0,"account_for_change_amount":null,"allocate_advances_automatically":0,"only_include_allocated_payments":0,"write_off_amount":0,"base_write_off_amount":0,"write_off_outstanding_amount_automatically":0,"write_off_account":null,"write_off_cost_center":null,"redeem_loyalty_points":0,"loyalty_points":0,"loyalty_amount":0,"loyalty_program":null,"loyalty_redemption_account":null,"loyalty_redemption_cost_center":null,"customer_address":"Kiboko ltd-Billing","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","contact_person":"Kiboko ltd-Kiboko ltd","contact_display":"Kiboko ltd","contact_mobile":"*********","contact_email":"<EMAIL>","territory":null,"shipping_address_name":"Kiboko ltd-Billing","shipping_address":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","dispatch_address_name":null,"dispatch_address":null,"company_address":null,"company_address_display":null,"company_contact_person":null,"ignore_default_payment_terms_template":0,"payment_terms_template":null,"tc_name":null,"terms":null,"po_no":"","po_date":null,"tra_control_number":null,"witholding_tax_certificate_number":null,"electronic_fiscal_device":null,"efd_z_report":null,"debit_to":"Debtors - DS","party_account_currency":"TZS","is_opening":"No","unrealized_profit_loss_account":null,"against_income_account":"Sales - DS","sales_partner":null,"amount_eligible_for_commission":1350000,"commission_rate":0,"total_commission":0,"letter_head":null,"group_same_items":0,"select_print_heading":null,"language":"en","subscription":null,"from_date":null,"auto_repeat":null,"to_date":null,"status":"Draft","inter_company_invoice_reference":null,"campaign":null,"represents_company":null,"source":null,"customer_group":null,"excise_duty_applicable":0,"is_internal_customer":0,"is_discounted":0,"remarks":null,"enabled_auto_create_delivery_notes":1,"delivery_status":"Not Delivered","doctype":"Sales Invoice","advances":[],"payments":[],"items":[{"name":"f1du3mqmge","owner":"Administrator","creation":"2025-03-20 08:58:14.186446","modified":"2025-03-20 08:58:14.186446","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":null,"has_item_scanned":0,"item_code":"Physical Verification","custom_uom":"Nos","is_ignored_in_pending_qty":0,"item_name":"Physical Verification","customer_item_code":null,"allow_over_sell":0,"description":"Physical Verification","item_group":"Services","brand":null,"image":"","qty":3,"stock_uom":"Nos","uom":"Nos","conversion_factor":1,"stock_qty":3,"price_list_rate":450000,"base_price_list_rate":450000,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":450000,"amount":1350000,"item_tax_template":null,"withholding_tax_rate":0,"withholding_tax_entry":null,"base_rate":450000,"base_amount":1350000,"pricing_rules":"","stock_uom_rate":450000,"is_free_item":0,"grant_commission":1,"csf_tz_wtax_jv_created":0,"net_rate":450000,"allow_override_net_rate":0,"net_amount":1350000,"base_net_rate":450000,"base_net_amount":1350000,"delivered_by_supplier":0,"income_account":"Sales - DS","is_fixed_asset":0,"asset":null,"finance_book":null,"expense_account":"Cost of Goods Sold - DS","discount_account":null,"deferred_revenue_account":null,"service_stop_date":null,"enable_deferred_revenue":0,"service_start_date":null,"service_end_date":null,"weight_per_unit":0,"total_weight":0,"weight_uom":null,"warehouse":"Stores - DS","target_warehouse":null,"quality_inspection":null,"serial_and_batch_bundle":null,"use_serial_batch_fields":1,"allow_zero_valuation_rate":0,"incoming_rate":0,"item_tax_rate":"{}","actual_batch_qty":0,"serial_no":null,"batch_no":null,"actual_qty":0,"company_total_stock":0,"sales_order":null,"delivery_status":"Not Delivered","so_detail":null,"sales_invoice_item":null,"delivery_note":null,"dn_detail":null,"delivered_qty":0,"purchase_order":null,"purchase_order_item":null,"cost_center":"Main - DS","project":null,"page_break":0,"custom_trade_in_item":null,"custom_trade_in_qty":0,"custom_trade_in_uom":null,"custom_trade_in_incoming_rate":0,"custom_total_trade_in_value":0,"custom_trade_in_batch_no":null,"custom_trade_in_serial_no":null,"parent":"ACC-SINV-2025-00002","parentfield":"items","parenttype":"Sales Invoice","doctype":"Sales Invoice Item","__unsaved":1}],"timesheets":[],"packed_items":[],"pricing_rules":[],"taxes":[],"payment_schedule":[{"name":"21hh085a3c","owner":"Administrator","creation":"2025-03-20 08:58:14.398178","modified":"2025-03-20 08:58:14.398178","modified_by":"Administrator","docstatus":0,"idx":1,"payment_term":null,"description":null,"due_date":"2025-03-20","mode_of_payment":null,"invoice_portion":100,"discount_type":null,"discount_date":null,"discount":0,"payment_amount":1350000,"outstanding":1350000,"paid_amount":0,"discounted_amount":0,"base_payment_amount":1350000,"parent":"ACC-SINV-2025-00002","parentfield":"payment_schedule","parenttype":"Sales Invoice","doctype":"Payment Schedule"}],"sales_team":[],"__onload":{"make_payment_via_journal_entry":0},"__last_sync_on":"2025-03-20T05:58:14.555Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 09:00:43,057 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Weekly Target","name":"new-weekly-target-yvfqglghbc","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"Design System","week_no":1,"week_start_date":"2025-03-20","week_end_date":"2025-03-27","target_amount":500000,"department":"Accounts","cost_center":"Main - DS","title":"week"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 09:14:26,513 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"ACC-SINV-2025-00002","owner":"Administrator","creation":"2025-03-20 08:58:14.186446","modified":"2025-03-20 08:58:14.186446","modified_by":"Administrator","docstatus":0,"idx":0,"title":"Kiboko ltd","naming_series":"ACC-SINV-.YYYY.-","customer":"Kiboko ltd","customer_name":"Kiboko ltd","tax_id":null,"company":"Design System","company_tax_id":null,"posting_date":"2025-03-20","posting_time":"08:58:14.337189","set_posting_time":0,"due_date":"2025-03-20","is_pos":0,"pos_profile":null,"is_consolidated":0,"is_return":0,"previous_invoice_number":null,"return_against":null,"update_outstanding_for_self":1,"update_billed_amount_in_sales_order":0,"update_billed_amount_in_delivery_note":1,"is_debit_note":0,"amended_from":null,"authotp_method":null,"authotp_validated":0,"cost_center":null,"project":null,"currency":"TZS","conversion_rate":1,"selling_price_list":"Standard Selling","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"scan_barcode":null,"update_stock":0,"custom_is_trade_in":0,"default_item_discount":0,"default_item_tax_template":null,"set_warehouse":null,"set_target_warehouse":null,"total_qty":3,"total_net_weight":0,"base_total":1350000,"base_net_total":1350000,"price_reduction":0,"total":1350000,"net_total":1350000,"tax_category":"","taxes_and_charges":null,"shipping_rule":null,"incoterm":null,"named_place":null,"base_total_taxes_and_charges":0,"total_taxes_and_charges":0,"base_grand_total":1350000,"base_rounding_adjustment":0,"base_rounded_total":1350000,"base_in_words":"","grand_total":1350000,"rounding_adjustment":0,"use_company_roundoff_cost_center":0,"rounded_total":1350000,"in_words":"","total_advance":0,"outstanding_amount":1350000,"disable_rounded_total":0,"apply_discount_on":"Grand Total","base_discount_amount":0,"is_cash_or_non_trade_discount":0,"additional_discount_account":null,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":null,"total_billing_hours":0,"total_billing_amount":0,"cash_bank_account":null,"base_paid_amount":0,"paid_amount":0,"base_change_amount":0,"change_amount":0,"account_for_change_amount":null,"allocate_advances_automatically":0,"only_include_allocated_payments":0,"write_off_amount":0,"base_write_off_amount":0,"write_off_outstanding_amount_automatically":0,"write_off_account":null,"write_off_cost_center":null,"redeem_loyalty_points":0,"loyalty_points":0,"loyalty_amount":0,"loyalty_program":null,"loyalty_redemption_account":null,"loyalty_redemption_cost_center":null,"customer_address":"Kiboko ltd-Billing","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","contact_person":"Kiboko ltd-Kiboko ltd","contact_display":"Kiboko ltd","contact_mobile":"*********","contact_email":"<EMAIL>","territory":null,"shipping_address_name":"Kiboko ltd-Billing","shipping_address":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","dispatch_address_name":null,"dispatch_address":null,"company_address":null,"company_address_display":null,"company_contact_person":null,"ignore_default_payment_terms_template":0,"payment_terms_template":null,"tc_name":null,"terms":null,"po_no":"","po_date":null,"tra_control_number":null,"witholding_tax_certificate_number":null,"electronic_fiscal_device":null,"efd_z_report":null,"debit_to":"Debtors - DS","party_account_currency":"TZS","is_opening":"No","unrealized_profit_loss_account":null,"against_income_account":"Sales - DS","sales_partner":null,"amount_eligible_for_commission":1350000,"commission_rate":0,"total_commission":0,"letter_head":null,"group_same_items":0,"select_print_heading":null,"language":"en","subscription":null,"from_date":null,"auto_repeat":null,"to_date":null,"status":"Draft","inter_company_invoice_reference":null,"campaign":null,"represents_company":null,"source":null,"customer_group":null,"excise_duty_applicable":0,"is_internal_customer":0,"is_discounted":0,"remarks":null,"enabled_auto_create_delivery_notes":1,"delivery_status":"Not Delivered","doctype":"Sales Invoice","advances":[],"payments":[],"items":[{"name":"f1du3mqmge","owner":"Administrator","creation":"2025-03-20 08:58:14.186446","modified":"2025-03-20 08:58:14.186446","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":null,"has_item_scanned":0,"item_code":"Physical Verification","custom_uom":"Nos","is_ignored_in_pending_qty":0,"item_name":"Physical Verification","customer_item_code":null,"allow_over_sell":0,"description":"Physical Verification","item_group":"Services","brand":null,"image":"","qty":3,"stock_uom":"Nos","uom":"Nos","conversion_factor":1,"stock_qty":3,"price_list_rate":450000,"base_price_list_rate":450000,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":450000,"amount":1350000,"item_tax_template":null,"withholding_tax_rate":0,"withholding_tax_entry":null,"base_rate":450000,"base_amount":1350000,"pricing_rules":"","stock_uom_rate":450000,"is_free_item":0,"grant_commission":1,"csf_tz_wtax_jv_created":0,"net_rate":450000,"allow_override_net_rate":0,"net_amount":1350000,"base_net_rate":450000,"base_net_amount":1350000,"delivered_by_supplier":0,"income_account":"Sales - DS","is_fixed_asset":0,"asset":null,"finance_book":null,"expense_account":"Cost of Goods Sold - DS","discount_account":null,"deferred_revenue_account":null,"service_stop_date":null,"enable_deferred_revenue":0,"service_start_date":null,"service_end_date":null,"weight_per_unit":0,"total_weight":0,"weight_uom":null,"warehouse":"Stores - DS","target_warehouse":null,"quality_inspection":null,"serial_and_batch_bundle":null,"use_serial_batch_fields":1,"allow_zero_valuation_rate":0,"incoming_rate":0,"item_tax_rate":"{}","actual_batch_qty":0,"serial_no":null,"batch_no":null,"actual_qty":0,"company_total_stock":0,"sales_order":null,"delivery_status":"Not Delivered","so_detail":null,"sales_invoice_item":null,"delivery_note":null,"dn_detail":null,"delivered_qty":0,"purchase_order":null,"purchase_order_item":null,"cost_center":"Main - DS","project":null,"page_break":0,"custom_trade_in_item":null,"custom_trade_in_qty":0,"custom_trade_in_uom":null,"custom_trade_in_incoming_rate":0,"custom_total_trade_in_value":0,"custom_trade_in_batch_no":null,"custom_trade_in_serial_no":null,"parent":"ACC-SINV-2025-00002","parentfield":"items","parenttype":"Sales Invoice","doctype":"Sales Invoice Item","__unsaved":1}],"timesheets":[],"packed_items":[],"pricing_rules":[],"taxes":[],"payment_schedule":[{"name":"21hh085a3c","owner":"Administrator","creation":"2025-03-20 08:58:14.398178","modified":"2025-03-20 08:58:14.398178","modified_by":"Administrator","docstatus":0,"idx":1,"payment_term":null,"description":null,"due_date":"2025-03-20","mode_of_payment":null,"invoice_portion":100,"discount_type":null,"discount_date":null,"discount":0,"payment_amount":1350000,"outstanding":1350000,"paid_amount":0,"discounted_amount":0,"base_payment_amount":1350000,"parent":"ACC-SINV-2025-00002","parentfield":"payment_schedule","parenttype":"Sales Invoice","doctype":"Payment Schedule"}],"sales_team":[],"__onload":{"make_payment_via_journal_entry":0},"__last_sync_on":"2025-03-20T05:58:14.555Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 09:18:40,834 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"ACC-SINV-2025-00002","owner":"Administrator","creation":"2025-03-20 08:58:14.186446","modified":"2025-03-20 08:58:14.186446","modified_by":"Administrator","docstatus":0,"idx":0,"title":"Kiboko ltd","naming_series":"ACC-SINV-.YYYY.-","customer":"Kiboko ltd","customer_name":"Kiboko ltd","tax_id":null,"company":"Design System","company_tax_id":null,"posting_date":"2025-03-20","posting_time":"08:58:14.337189","set_posting_time":0,"due_date":"2025-03-20","is_pos":0,"pos_profile":null,"is_consolidated":0,"is_return":0,"previous_invoice_number":null,"return_against":null,"update_outstanding_for_self":1,"update_billed_amount_in_sales_order":0,"update_billed_amount_in_delivery_note":1,"is_debit_note":0,"amended_from":null,"authotp_method":null,"authotp_validated":0,"cost_center":null,"project":null,"currency":"TZS","conversion_rate":1,"selling_price_list":"Standard Selling","price_list_currency":"TZS","plc_conversion_rate":1,"ignore_pricing_rule":0,"scan_barcode":null,"update_stock":0,"custom_is_trade_in":0,"default_item_discount":0,"default_item_tax_template":null,"set_warehouse":null,"set_target_warehouse":null,"total_qty":3,"total_net_weight":0,"base_total":1350000,"base_net_total":1350000,"price_reduction":0,"total":1350000,"net_total":1350000,"tax_category":"","taxes_and_charges":null,"shipping_rule":null,"incoterm":null,"named_place":null,"base_total_taxes_and_charges":0,"total_taxes_and_charges":0,"base_grand_total":1350000,"base_rounding_adjustment":0,"base_rounded_total":1350000,"base_in_words":"","grand_total":1350000,"rounding_adjustment":0,"use_company_roundoff_cost_center":0,"rounded_total":1350000,"in_words":"","total_advance":0,"outstanding_amount":1350000,"disable_rounded_total":0,"apply_discount_on":"Grand Total","base_discount_amount":0,"is_cash_or_non_trade_discount":0,"additional_discount_account":null,"additional_discount_percentage":0,"discount_amount":0,"other_charges_calculation":null,"total_billing_hours":0,"total_billing_amount":0,"cash_bank_account":null,"base_paid_amount":0,"paid_amount":0,"base_change_amount":0,"change_amount":0,"account_for_change_amount":null,"allocate_advances_automatically":0,"only_include_allocated_payments":0,"write_off_amount":0,"base_write_off_amount":0,"write_off_outstanding_amount_automatically":0,"write_off_account":null,"write_off_cost_center":null,"redeem_loyalty_points":0,"loyalty_points":0,"loyalty_amount":0,"loyalty_program":null,"loyalty_redemption_account":null,"loyalty_redemption_cost_center":null,"customer_address":"Kiboko ltd-Billing","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","contact_person":"Kiboko ltd-Kiboko ltd","contact_display":"Kiboko ltd","contact_mobile":"*********","contact_email":"<EMAIL>","territory":null,"shipping_address_name":"Kiboko ltd-Billing","shipping_address":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","dispatch_address_name":null,"dispatch_address":null,"company_address":null,"company_address_display":null,"company_contact_person":null,"ignore_default_payment_terms_template":0,"payment_terms_template":null,"tc_name":null,"terms":null,"po_no":"","po_date":null,"tra_control_number":null,"witholding_tax_certificate_number":null,"electronic_fiscal_device":null,"efd_z_report":null,"debit_to":"Debtors - DS","party_account_currency":"TZS","is_opening":"No","unrealized_profit_loss_account":null,"against_income_account":"Sales - DS","sales_partner":null,"amount_eligible_for_commission":1350000,"commission_rate":0,"total_commission":0,"letter_head":null,"group_same_items":0,"select_print_heading":null,"language":"en","subscription":null,"from_date":null,"auto_repeat":null,"to_date":null,"status":"Draft","inter_company_invoice_reference":null,"campaign":null,"represents_company":null,"source":null,"customer_group":null,"excise_duty_applicable":0,"is_internal_customer":0,"is_discounted":0,"remarks":null,"enabled_auto_create_delivery_notes":1,"delivery_status":"Not Delivered","doctype":"Sales Invoice","advances":[],"payments":[],"items":[{"name":"f1du3mqmge","owner":"Administrator","creation":"2025-03-20 08:58:14.186446","modified":"2025-03-20 08:58:14.186446","modified_by":"Administrator","docstatus":0,"idx":1,"barcode":null,"has_item_scanned":0,"item_code":"Physical Verification","custom_uom":"Nos","is_ignored_in_pending_qty":0,"item_name":"Physical Verification","customer_item_code":null,"allow_over_sell":0,"description":"Physical Verification","item_group":"Services","brand":null,"image":"","qty":3,"stock_uom":"Nos","uom":"Nos","conversion_factor":1,"stock_qty":3,"price_list_rate":450000,"base_price_list_rate":450000,"margin_type":"","margin_rate_or_amount":0,"rate_with_margin":0,"discount_percentage":0,"discount_amount":0,"base_rate_with_margin":0,"rate":450000,"amount":1350000,"item_tax_template":null,"withholding_tax_rate":0,"withholding_tax_entry":null,"base_rate":450000,"base_amount":1350000,"pricing_rules":"","stock_uom_rate":450000,"is_free_item":0,"grant_commission":1,"csf_tz_wtax_jv_created":0,"net_rate":450000,"allow_override_net_rate":0,"net_amount":1350000,"base_net_rate":450000,"base_net_amount":1350000,"delivered_by_supplier":0,"income_account":"Sales - DS","is_fixed_asset":0,"asset":null,"finance_book":null,"expense_account":"Cost of Goods Sold - DS","discount_account":null,"deferred_revenue_account":null,"service_stop_date":null,"enable_deferred_revenue":0,"service_start_date":null,"service_end_date":null,"weight_per_unit":0,"total_weight":0,"weight_uom":null,"warehouse":"Stores - DS","target_warehouse":null,"quality_inspection":null,"serial_and_batch_bundle":null,"use_serial_batch_fields":1,"allow_zero_valuation_rate":0,"incoming_rate":0,"item_tax_rate":"{}","actual_batch_qty":0,"serial_no":null,"batch_no":null,"actual_qty":0,"company_total_stock":0,"sales_order":null,"delivery_status":"Not Delivered","so_detail":null,"sales_invoice_item":null,"delivery_note":null,"dn_detail":null,"delivered_qty":0,"purchase_order":null,"purchase_order_item":null,"cost_center":"Main - DS","project":null,"page_break":0,"custom_trade_in_item":null,"custom_trade_in_qty":0,"custom_trade_in_uom":null,"custom_trade_in_incoming_rate":0,"custom_total_trade_in_value":0,"custom_trade_in_batch_no":null,"custom_trade_in_serial_no":null,"parent":"ACC-SINV-2025-00002","parentfield":"items","parenttype":"Sales Invoice","doctype":"Sales Invoice Item","__unsaved":1}],"timesheets":[],"packed_items":[],"pricing_rules":[],"taxes":[],"payment_schedule":[{"name":"21hh085a3c","owner":"Administrator","creation":"2025-03-20 08:58:14.398178","modified":"2025-03-20 08:58:14.398178","modified_by":"Administrator","docstatus":0,"idx":1,"payment_term":null,"description":null,"due_date":"2025-03-20","mode_of_payment":null,"invoice_portion":100,"discount_type":null,"discount_date":null,"discount":0,"payment_amount":1350000,"outstanding":1350000,"paid_amount":0,"discounted_amount":0,"base_payment_amount":1350000,"parent":"ACC-SINV-2025-00002","parentfield":"payment_schedule","parenttype":"Sales Invoice","doctype":"Payment Schedule"}],"sales_team":[],"__onload":{"make_payment_via_journal_entry":0},"__last_sync_on":"2025-03-20T05:58:14.555Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 09:25:23,339 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'module-def'}
2025-03-20 09:25:40,028 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'module-def'}
2025-03-20 09:40:45,741 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Weekly Target","name":"new-weekly-target-tdohhixumj","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"Design System","title":"week","week_no":3,"week_start_date":"2025-03-19","week_end_date":"2025-03-24","target_amount":400000,"department":"Accounts","cost_center":"Main - DS"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 18:12:02,615 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Weekly Target Revenue","name":"new-weekly-target-revenue-kegdzbknfi","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"Design System","accounting_dimensions":[{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-dklptfgdsa","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-kegdzbknfi","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":1,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-zbenacihuw","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-kegdzbknfi","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":2,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-ptokohegth","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-kegdzbknfi","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":3,"__unedited":false,"accounting_dimension":"Project"}],"title":"week","week_start_date":"2025-03-20","week_end_date":"2025-03-27","target_amount":500000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 18:18:20,600 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Weekly Target Revenue', 'name': 'new-weekly-target-revenue-kegdzbknfi', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-03-20 18:19:27,634 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Weekly Target Revenue","name":"new-weekly-target-revenue-oxlxrhpuaj","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"Design System","accounting_dimensions":[{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-lxixvwqdkd","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-oxlxrhpuaj","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":1,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-xuvlbudnyc","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-oxlxrhpuaj","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":2,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-stlnupsiai","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-oxlxrhpuaj","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":3,"__unedited":false,"accounting_dimension":"Project"}],"title":"week","week_start_date":"2025-03-20","week_end_date":"2025-03-27","target_amount":5000000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 18:20:50,493 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Weekly Target Revenue","name":"new-weekly-target-revenue-oxlxrhpuaj","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"Design System","accounting_dimensions":[{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-lxixvwqdkd","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-oxlxrhpuaj","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":1,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-xuvlbudnyc","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-oxlxrhpuaj","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":2,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-stlnupsiai","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-oxlxrhpuaj","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":3,"__unedited":false,"accounting_dimension":"Project"}],"title":"week","week_start_date":"2025-03-20","week_end_date":"2025-03-27","target_amount":5000000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-20 23:14:53,789 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Weekly Target Revenue","name":"new-weekly-target-revenue-yqsasyohxm","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"Design System","accounting_dimensions":[{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-hatqdmcesc","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-yqsasyohxm","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":1,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-sulvgrhizo","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-yqsasyohxm","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":2,"__unedited":false,"accounting_dimension":"Cost Center"},{"docstatus":0,"doctype":"Weekly Dimension Accounting","name":"new-weekly-dimension-accounting-tbeexmkovr","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-weekly-target-revenue-yqsasyohxm","parentfield":"accounting_dimensions","parenttype":"Weekly Target Revenue","idx":3,"__unedited":false,"accounting_dimension":"Project"}],"title":"wek2","week_start_date":"2025-03-20","week_end_date":"2025-03-27","target_amount":500000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-21 15:24:05,525 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'cmd': 'frappe.www.login.send_login_link', 'email': '<EMAIL>'}
2025-04-01 16:12:32,264 ERROR frappe Could not take error snapshot: No module named 'clearing.hooks'
Site: working
Form Dict: {'doctype': 'DocType', 'fields': '["`tabDocType`.`name`","`tabDocType`.`owner`","`tabDocType`.`creation`","`tabDocType`.`modified`","`tabDocType`.`modified_by`","`tabDocType`.`_user_tags`","`tabDocType`.`_comments`","`tabDocType`.`_assign`","`tabDocType`.`_liked_by`","`tabDocType`.`docstatus`","`tabDocType`.`idx`","`tabDocType`.`module`","`tabDocType`.`color`"]', 'filters': '[["DocType","module","=","Flexible Budget"]]', 'order_by': '`tabDocType`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 198, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1601, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1570, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1436, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'clearing.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1299, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1601, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1570, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1436, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'clearing.hooks'
2025-04-01 16:12:32,269 ERROR frappe Failed to run after request hook
Site: working
Form Dict: {'doctype': 'DocType', 'fields': '["`tabDocType`.`name`","`tabDocType`.`owner`","`tabDocType`.`creation`","`tabDocType`.`modified`","`tabDocType`.`modified_by`","`tabDocType`.`_user_tags`","`tabDocType`.`_comments`","`tabDocType`.`_assign`","`tabDocType`.`_liked_by`","`tabDocType`.`docstatus`","`tabDocType`.`idx`","`tabDocType`.`module`","`tabDocType`.`color`"]', 'filters': '[["DocType","module","=","Flexible Budget"]]', 'order_by': '`tabDocType`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1601, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1570, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1436, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'clearing.hooks'
2025-04-01 16:14:32,156 ERROR frappe Could not take error snapshot: No module named 'clearing.hooks'
Site: working
Form Dict: {'doctype': 'Clearing File', 'fields': '["`tabClearing File`.`name`","`tabClearing File`.`owner`","`tabClearing File`.`creation`","`tabClearing File`.`modified`","`tabClearing File`.`modified_by`","`tabClearing File`.`_user_tags`","`tabClearing File`.`_comments`","`tabClearing File`.`_assign`","`tabClearing File`.`_liked_by`","`tabClearing File`.`docstatus`","`tabClearing File`.`idx`","`tabClearing File`.`status`","`tabClearing File`.`customer`","`tabClearing File`.`mode_of_transport`","`tabClearing File`.`arrival_date`","`tabClearing File`.`cargo_country_of_origin`","`tabClearing File`.`cargo_description`","`tabClearing File`.`cargo_location`"]', 'filters': '[]', 'order_by': '`tabClearing File`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 198, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1601, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1570, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1436, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'clearing.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1299, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1601, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1570, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1436, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'clearing.hooks'
2025-04-01 16:14:32,159 ERROR frappe Failed to run after request hook
Site: working
Form Dict: {'doctype': 'Clearing File', 'fields': '["`tabClearing File`.`name`","`tabClearing File`.`owner`","`tabClearing File`.`creation`","`tabClearing File`.`modified`","`tabClearing File`.`modified_by`","`tabClearing File`.`_user_tags`","`tabClearing File`.`_comments`","`tabClearing File`.`_assign`","`tabClearing File`.`_liked_by`","`tabClearing File`.`docstatus`","`tabClearing File`.`idx`","`tabClearing File`.`status`","`tabClearing File`.`customer`","`tabClearing File`.`mode_of_transport`","`tabClearing File`.`arrival_date`","`tabClearing File`.`cargo_country_of_origin`","`tabClearing File`.`cargo_description`","`tabClearing File`.`cargo_location`"]', 'filters': '[]', 'order_by': '`tabClearing File`.`modified` desc', 'start': '0', 'page_length': '20', 'view': 'List', 'group_by': '', 'with_comment_count': '1'}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1601, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1570, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1436, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'clearing.hooks'
2025-04-02 15:37:29,045 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"TRA-25-0149","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":0,"clearing_file":"CF-2025-0147","customer":"Kiboko ltd","mode_of_transport":"Air","posting_date":"2025-04-02","status":"Payment Completed","total_charges":45000,"paid_by_clearing_agent":1,"invoice_paid":1,"doctype":"TRA Clearance","document":[{"name":"npoh8r43o2","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":1,"clearing_document_id":"Import Duty Payment Receipt-CF-2025-0147-0150","document_name":"Import Duty Payment Receipt","document_received":1,"document_attributes":"","parent":"TRA-25-0149","parentfield":"document","parenttype":"TRA Clearance","doctype":"TRA Document"},{"name":"o0kkjmb3vu","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":2,"clearing_document_id":"Import Duty Payment Receipt-CF-2025-0147-0151","document_name":"Import Duty Payment Receipt","document_received":1,"document_attributes":"","parent":"TRA-25-0149","parentfield":"document","parenttype":"TRA Clearance","doctype":"TRA Document"}],"__last_sync_on":"2025-04-02T12:32:07.445Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-02 15:40:47,354 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"TRA-25-0149","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":0,"clearing_file":"CF-2025-0147","customer":"Kiboko ltd","mode_of_transport":"Air","posting_date":"2025-04-02","status":"Payment Completed","total_charges":45000,"paid_by_clearing_agent":1,"invoice_paid":1,"doctype":"TRA Clearance","document":[{"name":"npoh8r43o2","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":1,"clearing_document_id":"Import Duty Payment Receipt-CF-2025-0147-0150","document_name":"Import Duty Payment Receipt","document_received":1,"document_attributes":"","parent":"TRA-25-0149","parentfield":"document","parenttype":"TRA Clearance","doctype":"TRA Document"},{"name":"o0kkjmb3vu","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":2,"clearing_document_id":"Import Duty Payment Receipt-CF-2025-0147-0151","document_name":"Import Duty Payment Receipt","document_received":1,"document_attributes":"","parent":"TRA-25-0149","parentfield":"document","parenttype":"TRA Clearance","doctype":"TRA Document"}],"__last_sync_on":"2025-04-02T12:32:07.445Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-02 15:43:12,612 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"TRA-25-0149","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":0,"clearing_file":"CF-2025-0147","customer":"Kiboko ltd","mode_of_transport":"Air","posting_date":"2025-04-02","status":"Payment Completed","total_charges":45000,"paid_by_clearing_agent":1,"invoice_paid":1,"doctype":"TRA Clearance","document":[{"name":"npoh8r43o2","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":1,"clearing_document_id":"Import Duty Payment Receipt-CF-2025-0147-0150","document_name":"Import Duty Payment Receipt","document_received":1,"document_attributes":"","parent":"TRA-25-0149","parentfield":"document","parenttype":"TRA Clearance","doctype":"TRA Document"},{"name":"o0kkjmb3vu","owner":"Administrator","creation":"2025-04-02 14:43:59.291807","modified":"2025-04-02 14:44:53.199060","modified_by":"Administrator","docstatus":0,"idx":2,"clearing_document_id":"Import Duty Payment Receipt-CF-2025-0147-0151","document_name":"Import Duty Payment Receipt","document_received":1,"document_attributes":"","parent":"TRA-25-0149","parentfield":"document","parenttype":"TRA Clearance","doctype":"TRA Document"}],"__last_sync_on":"2025-04-02T12:42:50.054Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-02 15:44:17,642 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"doctype":"TRA Clearance","clearing_file":"CF-2025-0147","customer":"Kiboko ltd","status":"Payment Pending"}', 'cmd': 'frappe.client.insert'}
2025-04-02 15:44:22,335 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"doctype":"Physical Verification","clearing_file":"CF-2025-0147","customer":"Kiboko ltd","status":"Payment Pending"}', 'cmd': 'frappe.client.insert'}
2025-04-02 15:44:26,822 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"doctype":"Port Clearance","clearing_file":"CF-2025-0147","customer":"Kiboko ltd","status":"Unpaid"}', 'cmd': 'frappe.client.insert'}
2025-04-02 15:44:34,947 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"doctype":"TRA Clearance","clearing_file":"CF-2025-0147","customer":"Kiboko ltd","status":"Payment Pending"}', 'cmd': 'frappe.client.insert'}
2025-04-03 13:07:04,117 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'args': '{"posting_date":"2025-04-03","company":"Design System","party_type":"Customer","payment_type":"Receive","party":"Kiboko ltd","party_account":"Debtors - DS","from_posting_date":"2015-04-06","to_posting_date":"2025-04-03","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-04-09 10:26:42,901 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 10:28:10,872 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 10:28:14,773 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 10:30:20,640 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 10:33:35,156 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 11:46:13,581 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'NHIMA Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 11:53:41,824 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'NHIMA Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-09 12:40:40,055 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'NHIMA Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-04-15 13:23:30,832 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-exfnuzeflk","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zcpofolqkt","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-exfnuzeflk","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"this cargo","value":75000,"weight":8000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"Kiboko ltd","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","customer_address":"Kiboko ltd-Billing","shipper":"John Doe","airline":"","departure_date":"2025-04-18","arrival_date":"2025-04-29","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 13:23:55,732 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-exfnuzeflk","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zcpofolqkt","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-exfnuzeflk","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"this cargo","value":75000,"weight":8000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"Kiboko ltd","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","customer_address":"Kiboko ltd-Billing","shipper":"John Doe","airline":"","departure_date":"2025-04-18","arrival_date":"2025-04-29","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 13:24:10,170 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-exfnuzeflk","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zcpofolqkt","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-exfnuzeflk","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"this cargo","value":75000,"weight":8000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"Kiboko ltd","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","customer_address":"Kiboko ltd-Billing","shipper":"John Doe","airline":"","departure_date":"2025-04-18","arrival_date":"2025-04-29","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 14:08:03,634 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-iapppxlnkb","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-qjvnrtfdii","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-iapppxlnkb","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"cargo B","value":45000,"weight":35000}],"document":[],"declaration_type":"","cl_plan":"","mode_of_transport":"Air","customer":"kibo","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\n23116<br>Tanzania<br>\\n<br>\\n","customer_address":"kibo-Billing","shipper":"John Doe","departure_date":"2025-04-18","arrival_date":"2025-04-30","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-15 14:11:31,370 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-pujhyairie","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-04-15","company":"Design System","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"Tanzania","cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-zozivubmhd","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 20FT","currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","country_of_destination":"Tanzania","parent":"new-clearing-file-pujhyairie","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"cargo b","cargo_description":"cargo b","value":500000,"weight":35000}],"document":[],"declaration_type":"","cl_plan":"","customer":"kibo","address_display":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\n23116<br>Tanzania<br>\\n<br>\\n","customer_address":"kibo-Billing","departure_date":"2025-04-18","arrival_date":"2025-04-30","shipper":"John Doe","mode_of_transport":"Air","cargo_location":"Dar es salaam"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-18 10:58:50,399 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'chart_name': 'Hiring vs Attrition Count', 'filters': '{"time_interval":"Monthly","company":"Design System"}', 'refresh': '1', 'time_interval': '', 'timespan': '', 'from_date': '', 'to_date': '', 'heatmap_year': '', 'cmd': 'hrms.hr.dashboard_chart_source.hiring_vs_attrition_count.hiring_vs_attrition_count.get_data'}
2025-04-19 18:42:59,756 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Accumulated Depreciation - DS","party_type":"","account_type":"Accumulated Depreciation","exchange_rate":1,"party":"","debit":0,"credit":250000,"debit_in_account_currency":null,"credit_in_account_currency":250000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":0,"total_credit":250000,"difference":-250000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:43:23,283 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Capital Equipments - DS","party_type":"","account_type":"Fixed Asset","exchange_rate":1,"party":"","debit":0,"credit":250000,"debit_in_account_currency":null,"credit_in_account_currency":250000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":0,"total_credit":250000,"difference":-250000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:44:55,361 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Expenses Included In Asset Valuation - DS","party_type":"","account_type":"Expenses Included In Asset Valuation","exchange_rate":1,"party":"","debit":150000,"credit":0,"debit_in_account_currency":150000,"credit_in_account_currency":0}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":150000,"total_credit":0,"difference":150000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:52:41,487 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"laptop depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":150000,"credit":0,"debit_in_account_currency":150000,"credit_in_account_currency":0}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":150000,"total_credit":0,"difference":150000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:54:50,235 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-rkyyezrpho","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-ynrvbxwazm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-rkyyezrpho","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"laptop depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":150000,"credit":0,"debit_in_account_currency":150000,"credit_in_account_currency":0}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-01","total_debit":150000,"total_credit":0,"difference":150000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 18:54:57,833 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'journal-entry/new-journal-entry-rkyyezrpho'}
2025-04-19 19:00:38,865 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-mgwybdfrvs","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-skvcpaausx","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-mgwybdfrvs","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Accumulated Depreciation - DS","party_type":"","account_type":"Accumulated Depreciation","exchange_rate":1,"party":"","debit":50000,"credit":0,"debit_in_account_currency":50000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-02","total_debit":50000,"total_credit":0,"difference":50000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 19:00:55,758 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-mgwybdfrvs","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Journal Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-skvcpaausx","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-mgwybdfrvs","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Accumulated Depreciation - DS","party_type":"","account_type":"Accumulated Depreciation","exchange_rate":1,"party":"","debit":50000,"credit":0,"debit_in_account_currency":50000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-02","total_debit":50000,"total_credit":0,"difference":50000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 19:06:05,187 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-bwgqwwuukn","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-pcxbhvgupi","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-bwgqwwuukn","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Laptop Depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":550000,"credit":0,"debit_in_account_currency":550000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-19","total_debit":550000,"total_credit":0,"difference":550000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-19 19:33:02,723 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Journal Entry","name":"new-journal-entry-bwgqwwuukn","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","is_system_generated":0,"voucher_type":"Depreciation Entry","naming_series":"ACC-JV-.YYYY.-","company":"Design System","apply_tds":0,"accounts":[{"docstatus":0,"doctype":"Journal Entry Account","name":"new-journal-entry-account-pcxbhvgupi","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","cost_center":"Main - DS","account_currency":"TZS","reference_type":"","is_advance":"No","parent":"new-journal-entry-bwgqwwuukn","parentfield":"accounts","parenttype":"Journal Entry","idx":1,"account":"Laptop Depreciation - DS","party_type":"","account_type":"Depreciation","exchange_rate":1,"party":"","debit":550000,"credit":0,"debit_in_account_currency":550000}],"multi_currency":0,"total_amount_currency":"TZS","write_off_based_on":"Accounts Receivable","is_opening":"No","posting_date":"2025-04-19","total_debit":550000,"total_credit":0,"difference":550000}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-22 14:07:11,440 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"CF-DN-2025-00282","owner":"<EMAIL>","creation":"2025-04-22 12:32:41.358742","modified":"2025-04-22 14:07:07.336379","modified_by":"<EMAIL>","docstatus":0,"idx":0,"clearing_file":"CF-2025-0204","consignee":"Kiboko ltd","posting_date":"2025-04-22","address":"Sombetini<br>\\nSimanjiro<br>Arusha<br>\\nArusha<br>23116<br>Tanzania<br>\\n<br>\\n","exporter_type":"In-house","has_container_interchange":0,"doctype":"CF Delivery Note","truck":[],"delivery_date":"2025-04-23","loading_date":"2025-04-23"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-04-28 17:06:21,732 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCF Employee', 'filters': '{"from_date":"2025-01-01","to_date":"2025-04-28"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-05-17 14:08:09,886 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'recipients': '<EMAIL>, ', 'subject': 'Client Script: stock entry script', 'content': '<div class="ql-editor read-mode"><p>that is client script</p></div>', 'doctype': 'Client Script', 'name': 'stock entry script', 'send_email': '1', 'print_html': '', 'send_me_a_copy': '0', 'print_format': 'Standard', 'attachments': '[]', 'read_receipt': '0', 'print_letterhead': '1', 'send_after': '', 'print_language': 'en', 'cmd': 'frappe.core.doctype.communication.email.make'}
2025-05-26 08:59:38,195 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-03 17:55:09,239 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-03 17:55:09,271 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-14 19:45:23,781 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-06-23 18:37:47,606 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"CC-CF-2025-0438-00450","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":0,"clearing_file":"CF-2025-0438","status":"Paid","consigee":"Customer 19","tra_clearance_total":30000,"port_clearance_total":30000,"shipment_clearance_total":0,"physical_clearance_total":45677,"total_charges_sum":105677,"invoice_number":"ACC-SINV-2025-00038","debit_note_number":"ACC-SINV-2025-00036","doctype":"Clearing Charges","charges":[{"name":"2j8jodevbn","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":1,"charge_type":"Port Clearance","currency":"TZS","amount":30000,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"},{"name":"e92f9f1r74","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":2,"charge_type":"Shipment Clearance","currency":"TZS","amount":39000,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"},{"name":"l4213mio47","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":3,"charge_type":"Physical Verification","currency":"TZS","amount":45677,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"},{"name":"uuitlp1eh0","owner":"<EMAIL>","creation":"2025-03-14 08:15:17.777610","modified":"2025-06-23 10:53:09.724693","modified_by":"Administrator","docstatus":0,"idx":4,"charge_type":"TRA Clearance","currency":"TZS","amount":30000,"parent":"CC-CF-2025-0438-00450","parentfield":"charges","parenttype":"Clearing Charges","doctype":"Clearing Charge Detail"}],"service_charges":[{"docstatus":0,"doctype":"Clearing Service Charge Detail","name":"new-clearing-service-charge-detail-wjwemuqwac","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"CC-CF-2025-0438-00450","parentfield":"service_charges","parenttype":"Clearing Charges","idx":1,"__unedited":true}],"__last_sync_on":"2025-06-23T15:37:31.646Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-23 18:46:33,472 ERROR frappe Error while inserting deferred Error Log record: Error Log 93uqqjedho: 'Title' (Module import failed for Clearing Service Charge Detail, the DocType you're trying to open might be deleted.
Error: No module named 'clearing.clearing.doctype.clearing_service_charge_detail.clearing_service_charge_detail') will get truncated, as max characters allowed is 140
Site: working
Form Dict: {}
2025-06-25 11:15:54,065 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'sales-invoice/ACC-SINV-2025-00045'}
2025-06-25 11:15:54,068 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'sales-invoice/ACC-SINV-2025-00045'}
2025-06-25 11:15:55,724 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'sales-invoice/ACC-SINV-2025-00045'}
2025-07-27 14:58:38,828 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"CF Delivery Note","name":"new-cf-delivery-note-lqhlnredae","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-07-27","exporter_type":"In-house","truck":[],"has_container_interchange":1,"consignee":"Customer 21","address":"","clearing_file":"CF-2025-0357","delivery_date":"2025-07-28","loading_date":"2025-07-29","delivery_location":"Tanga","staff_name":"LYRIC  GRAMA NAWAO","staff_id":"HR-EMP-00001","return_date":"2025-07-30"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-08 14:24:22,940 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-09-12 09:32:44,720 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:44:47,795 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:53:26,112 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:54:17,841 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:56:50,997 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:57:05,372 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 10:41:05,375 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Shipping Line Clearance Summary', 'filters': '{"from_date":"2025-01-01","to_date":"2025-09-12"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 10:52:59,486 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Shipping Line Clearance Summary', 'filters': '{"from_date":"2025-01-01","to_date":"2025-09-12"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-15 16:06:41,519 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gqhtcjcuyr","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Cleared","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-mzpdlivpgs","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gqhtcjcuyr","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","hs_code":"84159000","container_number":"MSMU5069182","seal_number":"**********","value":12374,"invoice_ref":"7632162-7634292","quantity_of_hs_code":1,"quantity_of_container":1,"number_of_packages":7,"weight":448,"volume":11.49}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:07:21,686 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gqhtcjcuyr","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Cleared","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-mzpdlivpgs","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gqhtcjcuyr","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","hs_code":"84159000","container_number":"MSMU5069182","seal_number":"**********","value":12374,"invoice_ref":"7632162-7634292","quantity_of_hs_code":1,"quantity_of_container":1,"number_of_packages":7,"weight":448,"volume":11.49}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:11:08,010 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gsnlbrqyih","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-fprjfbacxj","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gsnlbrqyih","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"84159000","container_number":"MSMU5069182","quantity_of_hs_code":1,"quantity_of_container":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","number_of_packages":7,"seal_number":"**********","weight":448,"volume":11.49,"value":12374,"invoice_ref":"7632162-7634292"}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:12:16,091 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gsnlbrqyih","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-fprjfbacxj","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gsnlbrqyih","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"84159000","container_number":"MSMU5069182","quantity_of_hs_code":1,"quantity_of_container":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","number_of_packages":7,"seal_number":"**********","weight":448,"volume":11.49,"value":12374,"invoice_ref":"7632162-7634292"}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:12:22,086 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gsnlbrqyih","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-fprjfbacxj","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gsnlbrqyih","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"84159000","container_number":"MSMU5069182","quantity_of_hs_code":1,"quantity_of_container":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","number_of_packages":7,"seal_number":"**********","weight":448,"volume":11.49,"value":12374,"invoice_ref":"7632162-7634292"}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-17 11:34:21,782 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Bond Register Report', 'filters': '{"from_date":"2025-01-01","to_date":"2025-09-17"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-18 14:29:58,914 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:29:59,290 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:00,332 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:01,571 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:01,863 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:16,889 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:17,125 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 16:19:05,053 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'clearing-charges/CC-CF-2025-0708-00723'}
2025-09-18 16:19:40,076 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'clearing-charges/CC-CF-2025-0708-00723'}
2025-09-18 16:29:25,043 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'clearing-charges/CC-CF-2025-0708-00723'}
