2025-08-21 11:24:43,865 ERROR frappe Failed to run after request hook
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 19, in get_response
    endpoint, renderer_instance = path_resolver.resolve()
                                  ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 38, in resolve
    resolve_redirect(self.path, request.query_string)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 117, in resolve_redirect
    redirects = frappe.get_hooks("website_redirects")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 133, in application
    response = handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/permissions.py", line 883, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 396, in handle_exception
    response = get_response("message", http_status_code=http_status_code)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 23, in get_response
    return handle_exception(e, endpoint, path, http_status_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/permissions.py", line 883, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 37, in handle_exception
    return ErrorPage(exception=e).render()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/error_page.py", line 7, in __init__
    super().__init__(path=path, http_status_code=http_status_code)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 45, in __init__
    self.set_template_path()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 54, in set_template_path
    app_path = frappe.get_app_path(app)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:24:43,865 ERROR frappe Failed to run after request hook
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 19, in get_response
    endpoint, renderer_instance = path_resolver.resolve()
                                  ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 38, in resolve
    resolve_redirect(self.path, request.query_string)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 117, in resolve_redirect
    redirects = frappe.get_hooks("website_redirects")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 133, in application
    response = handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/permissions.py", line 883, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 396, in handle_exception
    response = get_response("message", http_status_code=http_status_code)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 23, in get_response
    return handle_exception(e, endpoint, path, http_status_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/permissions.py", line 883, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 37, in handle_exception
    return ErrorPage(exception=e).render()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/error_page.py", line 7, in __init__
    super().__init__(path=path, http_status_code=http_status_code)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 45, in __init__
    self.set_template_path()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 54, in set_template_path
    app_path = frappe.get_app_path(app)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:24:43,868 ERROR frappe Failed to run after request hook
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:24:43,878 ERROR frappe Failed to run after request hook
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 197, in init_request
    for before_request_task in frappe.get_hooks("before_request"):
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 19, in get_response
    endpoint, renderer_instance = path_resolver.resolve()
                                  ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 38, in resolve
    resolve_redirect(self.path, request.query_string)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/path_resolver.py", line 117, in resolve_redirect
    redirects = frappe.get_hooks("website_redirects")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 133, in application
    response = handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/permissions.py", line 883, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 396, in handle_exception
    response = get_response("message", http_status_code=http_status_code)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 23, in get_response
    return handle_exception(e, endpoint, path, http_status_code)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/permissions.py", line 883, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/serve.py", line 37, in handle_exception
    return ErrorPage(exception=e).render()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/error_page.py", line 7, in __init__
    super().__init__(path=path, http_status_code=http_status_code)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 45, in __init__
    self.set_template_path()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/website/page_renderers/template_page.py", line 54, in set_template_path
    app_path = frappe.get_app_path(app)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:24:43,883 ERROR frappe Failed to run after request hook
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:24:43,895 ERROR frappe Failed to run after request hook
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:29:06,244 ERROR frappe Could not take error snapshot: No module named 'hrms'
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:29:09,825 ERROR frappe Could not take error snapshot: No module named 'hrms'
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:29:10,777 ERROR frappe Could not take error snapshot: No module named 'hrms'
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:29:11,264 ERROR frappe Could not take error snapshot: No module named 'hrms'
Site: clear
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'hrms'
2025-08-21 11:30:22,653 ERROR frappe New Exception collected in error log
Site: clear
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00084', 'cmd': 'frappe.client.delete'}
2025-08-21 11:32:23,660 ERROR frappe New Exception collected in error log
Site: clear
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00084', 'cmd': 'frappe.client.delete'}
2025-08-21 11:33:09,175 ERROR frappe New Exception collected in error log
Site: clear
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00084', 'cmd': 'frappe.client.delete'}
2025-08-21 11:35:10,710 ERROR frappe New Exception collected in error log
Site: clear
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00084', 'cmd': 'frappe.client.delete'}
2025-08-25 10:15:47,080 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doc': '{"name":"Sal Slip/HR-EMP-00006/00032","owner":"Administrator","creation":"2025-08-25 09:04:15.339383","modified":"2025-08-25 09:04:15.339383","modified_by":"Administrator","docstatus":0,"idx":165,"workflow_state":"Draft","employee":"HR-EMP-00006","employee_name":"Daudi Thomas Majinge","company":"Rubis Technical Services Limited","department":"Technical - A","designation":"Installer","branch":null,"posting_date":"2025-08-25","letter_head":"Rubis TZ","has_payroll_approval":0,"status":"Draft","salary_withholding":null,"salary_withholding_cycle":null,"currency":"TZS","exchange_rate":1,"payroll_frequency":"Monthly","start_date":"2025-08-01","end_date":"2025-08-31","salary_structure":"Full time structure","payroll_entry":"HR-PRUN-2025-00019","mode_of_payment":"Bank","salary_slip_based_on_timesheet":0,"deduct_tax_for_unclaimed_employee_benefits":0,"deduct_tax_for_unsubmitted_tax_exemption_proof":0,"total_working_days":25,"unmarked_days":0,"leave_without_pay":0,"absent_days":0,"payment_days":25,"total_working_hours":0,"hour_rate":0,"base_hour_rate":0,"gross_pay":600000,"base_gross_pay":600000,"gross_year_to_date":600000,"base_gross_year_to_date":0,"total_deduction":84000,"base_total_deduction":84000,"net_pay":516000,"base_net_pay":516000,"rounded_total":516000,"base_rounded_total":516000,"year_to_date":516000,"base_year_to_date":0,"month_to_date":516000,"base_month_to_date":0,"total_in_words":"TZS Five Hundred And Sixteen Thousand only.","base_total_in_words":"TZS Five Hundred And Sixteen Thousand only.","ctc":0,"income_from_other_sources":0,"total_earnings":0,"non_taxable_earnings":0,"standard_tax_exemption_amount":0,"tax_exemption_declaration":0,"deductions_before_tax_calculation":0,"annual_taxable_amount":0,"income_tax_deducted_till_date":0,"current_month_income_tax":0,"future_income_tax_deductions":0,"total_income_tax":0,"journal_entry":null,"amended_from":null,"bank_name":"CRDB","bank_account_no":"*************","doctype":"Salary Slip","salary_slip_ot_component":[],"leave_details":[{"name":"o5qdr20eeu","owner":"Administrator","creation":"2025-08-25 09:04:15.441509","modified":"2025-08-25 09:04:15.441509","modified_by":"Administrator","docstatus":0,"idx":1,"leave_type":"Annual Leave","total_allocated_leaves":14,"expired_leaves":0,"used_leaves":7,"pending_leaves":0,"available_leaves":7,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"leave_details","parenttype":"Salary Slip","doctype":"Salary Slip Leave"},{"name":"o5qur5l4lt","owner":"Administrator","creation":"2025-08-25 09:04:15.441933","modified":"2025-08-25 09:04:15.441933","modified_by":"Administrator","docstatus":0,"idx":2,"leave_type":"Compassionate Leave","total_allocated_leaves":4,"expired_leaves":0,"used_leaves":0,"pending_leaves":0,"available_leaves":4,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"leave_details","parenttype":"Salary Slip","doctype":"Salary Slip Leave"},{"name":"o5q64bjrsp","owner":"Administrator","creation":"2025-08-25 09:04:15.442773","modified":"2025-08-25 09:04:15.442773","modified_by":"Administrator","docstatus":0,"idx":3,"leave_type":"Paternity Leave","total_allocated_leaves":3,"expired_leaves":0,"used_leaves":0,"pending_leaves":0,"available_leaves":3,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"leave_details","parenttype":"Salary Slip","doctype":"Salary Slip Leave"},{"name":"o5qo9mcd1m","owner":"Administrator","creation":"2025-08-25 09:04:15.443219","modified":"2025-08-25 09:04:15.443219","modified_by":"Administrator","docstatus":0,"idx":4,"leave_type":"Sick Leave Full","total_allocated_leaves":63,"expired_leaves":0,"used_leaves":0,"pending_leaves":1,"available_leaves":63,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"leave_details","parenttype":"Salary Slip","doctype":"Salary Slip Leave"},{"name":"o5qevvijj6","owner":"Administrator","creation":"2025-08-25 09:04:15.443621","modified":"2025-08-25 09:04:15.443621","modified_by":"Administrator","docstatus":0,"idx":5,"leave_type":"Study Leave","total_allocated_leaves":5,"expired_leaves":0,"used_leaves":0,"pending_leaves":0,"available_leaves":5,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"leave_details","parenttype":"Salary Slip","doctype":"Salary Slip Leave"}],"earnings":[{"name":"o5qn5hn0mk","owner":"Administrator","creation":"2025-08-25 09:04:15.434070","modified":"2025-08-25 09:04:15.434070","modified_by":"Administrator","docstatus":0,"idx":1,"salary_component":"Basic","abbr":"B","amount":600000,"year_to_date":600000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":1,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":0,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":600000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"earnings","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qv1vk3gv","owner":"Administrator","creation":"2025-08-25 09:04:15.434672","modified":"2025-08-25 09:04:15.434672","modified_by":"Administrator","docstatus":0,"idx":2,"salary_component":"NHIF Expense","abbr":"NSSFExp_1","amount":40000,"year_to_date":40000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":40000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"earnings","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qa2dnapg","owner":"Administrator","creation":"2025-08-25 09:04:15.435238","modified":"2025-08-25 09:04:15.435238","modified_by":"Administrator","docstatus":0,"idx":3,"salary_component":"Bonus","abbr":"B_1","amount":0,"year_to_date":0,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":0,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":0,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"earnings","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qngrc9rc","owner":"Administrator","creation":"2025-08-25 09:04:15.435819","modified":"2025-08-25 09:04:15.435819","modified_by":"Administrator","docstatus":0,"idx":4,"salary_component":"NSSF Expense","abbr":"NSSFExp","amount":60000,"year_to_date":60000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":60000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"earnings","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qu97vbgu","owner":"Administrator","creation":"2025-08-25 09:04:15.436386","modified":"2025-08-25 09:04:15.436386","modified_by":"Administrator","docstatus":0,"idx":5,"salary_component":"WCF Expense","abbr":"WCFExp","amount":3000,"year_to_date":3000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":3000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"earnings","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qr96d4c1","owner":"Administrator","creation":"2025-08-25 09:04:15.436993","modified":"2025-08-25 09:04:15.436993","modified_by":"Administrator","docstatus":0,"idx":6,"salary_component":"SDL Expense","abbr":"SDLExp","amount":21000,"year_to_date":21000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":21000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"earnings","parenttype":"Salary Slip","doctype":"Salary Detail"}],"timesheets":[],"deductions":[{"name":"o5q2uf9ho6","owner":"Administrator","creation":"2025-08-25 09:04:15.437631","modified":"2025-08-25 09:04:15.437631","modified_by":"Administrator","docstatus":0,"idx":1,"salary_component":"NSSF Employer","abbr":"NSSFE","amount":60000,"year_to_date":60000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":60000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"deductions","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qicfc9n8","owner":"Administrator","creation":"2025-08-25 09:04:15.438187","modified":"2025-08-25 09:04:15.438187","modified_by":"Administrator","docstatus":0,"idx":2,"salary_component":"NSSF Employee","abbr":"NSSFEmp","amount":60000,"year_to_date":60000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":0,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":60000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"deductions","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qujj2kbf","owner":"Administrator","creation":"2025-08-25 09:04:15.438839","modified":"2025-08-25 09:04:15.438839","modified_by":"Administrator","docstatus":0,"idx":3,"salary_component":"WCF Payable","abbr":"WCFP","amount":3000,"year_to_date":3000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":3000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"deductions","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5q6889j1l","owner":"Administrator","creation":"2025-08-25 09:04:15.439456","modified":"2025-08-25 09:04:15.439456","modified_by":"Administrator","docstatus":0,"idx":4,"salary_component":"SDL Payable","abbr":"SDLP","amount":21000,"year_to_date":21000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":21000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"deductions","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5q1pvecvn","owner":"Administrator","creation":"2025-08-25 09:04:15.440159","modified":"2025-08-25 09:04:15.440159","modified_by":"Administrator","docstatus":0,"idx":5,"salary_component":"PAYE Payable","abbr":"PAYEP","amount":24000,"year_to_date":24000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":0,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":24000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"deductions","parenttype":"Salary Slip","doctype":"Salary Detail"},{"name":"o5qqsd3vl9","owner":"Administrator","creation":"2025-08-25 09:04:15.440747","modified":"2025-08-25 09:04:15.440747","modified_by":"Administrator","docstatus":0,"idx":6,"salary_component":"NHIF Employer","abbr":"NHIFE","amount":40000,"year_to_date":40000,"additional_salary":null,"is_recurring_additional_salary":0,"statistical_component":0,"depends_on_payment_days":0,"exempted_from_income_tax":0,"is_tax_applicable":0,"is_flexible_benefit":0,"variable_based_on_taxable_salary":0,"do_not_include_in_total":1,"do_not_include_in_accounts":0,"deduct_full_tax_on_selected_payroll_date":0,"condition":null,"amount_based_on_formula":0,"formula":null,"default_amount":40000,"additional_amount":0,"tax_on_flexible_benefit":0,"tax_on_additional_salary":0,"parent":"Sal Slip/HR-EMP-00006/00032","parentfield":"deductions","parenttype":"Salary Slip","doctype":"Salary Detail"}]}', 'cmd': 'frappe.client.submit'}
2025-08-28 10:52:38,641 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Sales Cycle Report', 'filters': '{"from_date":"2025-07-28","to_date":"2025-08-28"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-28 10:58:13,671 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Sales Cycle Report', 'filters': '{"from_date":"2025-08-01","to_date":"2025-08-28"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-28 11:00:43,284 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Sales Cycle Report', 'filters': '{"from_date":"2025-08-01","to_date":"2025-08-28"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-28 11:05:05,793 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Sales Cycle Report', 'filters': '{"from_date":"2025-08-01","to_date":"2025-08-28"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-08-31 21:28:50,470 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'cmd': 'frappe.desk.form.load.getdoc'}
2025-08-31 21:29:22,486 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'cmd': 'frappe.desk.form.load.getdoc'}
2025-09-01 22:05:59,213 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'payroll_entry': 'HR-PRUN-2025-00019', 'cmd': 'csf_tz.csftz_hooks.payroll.create_journal_entry'}
2025-09-01 22:06:08,924 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'payroll_entry': 'HR-PRUN-2025-00019', 'cmd': 'csf_tz.csftz_hooks.payroll.create_journal_entry'}
2025-09-06 14:51:39,980 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Sales Cycle Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-08 14:24:22,940 ERROR frappe Failed to capture exception
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-09-12 09:32:44,720 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:44:47,795 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:53:26,112 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:54:17,841 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:56:50,997 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 09:57:05,372 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Physical Verification Summary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 10:41:05,375 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Shipping Line Clearance Summary', 'filters': '{"from_date":"2025-01-01","to_date":"2025-09-12"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-12 10:52:59,486 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Shipping Line Clearance Summary', 'filters': '{"from_date":"2025-01-01","to_date":"2025-09-12"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-14 18:43:07,950 ERROR frappe New Exception collected in error log
Site: broad
Form Dict: {'report_name': 'Presenter Performance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-09-15 16:06:41,519 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gqhtcjcuyr","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Cleared","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-mzpdlivpgs","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gqhtcjcuyr","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","hs_code":"84159000","container_number":"MSMU5069182","seal_number":"**********","value":12374,"invoice_ref":"7632162-7634292","quantity_of_hs_code":1,"quantity_of_container":1,"number_of_packages":7,"weight":448,"volume":11.49}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:07:21,686 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gqhtcjcuyr","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Cleared","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-mzpdlivpgs","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gqhtcjcuyr","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","hs_code":"84159000","container_number":"MSMU5069182","seal_number":"**********","value":12374,"invoice_ref":"7632162-7634292","quantity_of_hs_code":1,"quantity_of_container":1,"number_of_packages":7,"weight":448,"volume":11.49}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:11:08,010 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gsnlbrqyih","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-fprjfbacxj","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gsnlbrqyih","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"84159000","container_number":"MSMU5069182","quantity_of_hs_code":1,"quantity_of_container":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","number_of_packages":7,"seal_number":"**********","weight":448,"volume":11.49,"value":12374,"invoice_ref":"7632162-7634292"}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:12:16,091 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gsnlbrqyih","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-fprjfbacxj","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gsnlbrqyih","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"84159000","container_number":"MSMU5069182","quantity_of_hs_code":1,"quantity_of_container":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","number_of_packages":7,"seal_number":"**********","weight":448,"volume":11.49,"value":12374,"invoice_ref":"7632162-7634292"}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 16:12:22,086 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"docstatus":0,"doctype":"Clearing File","name":"new-clearing-file-gsnlbrqyih","__islocal":1,"__unsaved":1,"owner":"Administrator","posting_date":"2025-09-15","company":"Fleet Management Company","status":"Open","shipment_type":"Import","bl_type":"Original B/L","cargo_country_of_origin":"United Kingdom","bond_returned":0,"cargo_details":[{"docstatus":0,"doctype":"Cargo","name":"new-cargo-fprjfbacxj","__islocal":1,"__unsaved":1,"owner":"Administrator","package_type":"Container 40FT","currency":"USD","trading_country":"Japan","country_last_consignment":"United Arab Emirates","country_of_destination":"Tanzania","parent":"new-clearing-file-gsnlbrqyih","parentfield":"cargo_details","parenttype":"Clearing File","idx":1,"hs_code":"84159000","container_number":"MSMU5069182","quantity_of_hs_code":1,"quantity_of_container":1,"cargo_description":"AIR CONDITIONER SPARE PARTS","number_of_packages":7,"seal_number":"**********","weight":448,"volume":11.49,"value":12374,"invoice_ref":"7632162-7634292"}],"document":[],"declaration_type":"","cl_plan":"","customer":"Virgin Plaza Ltd","address_display":"P.O. BOX 7177<br>Dar es salaam<br>\\nTanzania<br>\\nEmail: <EMAIL><br>","customer_address":"Virgin Plaza-Billing-1","shipper":"Toshiba Airconditioning","mode_of_transport":"Sea","carrier_name":"AHI Carrier Fzc","voyage_flight_number":"MSC MARIE NR415E","departure_date":"2025-09-01","arrival_date":"2025-09-22"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 21:29:31,560 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Loose Cargo Tracking', 'filters': '{"report_type":"Exited Loose Cargo"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-15 21:31:59,268 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Loose Cargo Tracking', 'filters': '{"report_type":"Exited Loose Cargo"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-15 22:14:17,799 ERROR frappe New Exception collected in error log
Site: broad
Form Dict: {'doc': '{"docstatus":0,"doctype":"Advertisement Broadcast","name":"new-advertisement-broadcast-jzluwieuyw","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"ADB-.YYYY.-","status":"Scheduled","priority":"Medium","auto_generate_invoice":1,"notification_sent":0,"broadcast_logs":[],"__run_link_triggers":1,"customer":"clouds","scheduled_date":"2025-09-15","scheduled_time":"22:38:13","duration_seconds":56,"rate_per_second":43,"presenter":"Administrator","advertisement_title":"CCM Campaign"}', 'cmd': 'frappe.client.save'}
2025-09-15 22:14:58,906 ERROR frappe New Exception collected in error log
Site: broad
Form Dict: {'doc': '{"docstatus":0,"doctype":"Advertisement Broadcast","name":"new-advertisement-broadcast-jzluwieuyw","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"ADB-.YYYY.-","status":"Scheduled","priority":"Medium","auto_generate_invoice":1,"notification_sent":0,"broadcast_logs":[],"__run_link_triggers":false,"customer":"clouds","scheduled_date":"2025-09-15","scheduled_time":"22:38:13","duration_seconds":56,"rate_per_second":43,"presenter":"Administrator","advertisement_title":"CCM Campaign"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-15 23:12:30,392 ERROR frappe New Exception collected in error log
Site: broad
Form Dict: {'doc': '{"docstatus":0,"doctype":"Advertisement Broadcast","name":"new-advertisement-broadcast-cmalowjlqx","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"ADB-.YYYY.-","status":"Aired","priority":"Medium","auto_generate_invoice":1,"notification_sent":0,"broadcast_logs":[],"__run_link_triggers":false,"customer":"clouds","scheduled_date":"2025-09-17","scheduled_time":"23:10:26","duration_seconds":60,"rate_per_second":150000,"presenter":"Administrator","advertisement_title":"Campaing"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-16 08:26:16,026 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Loose Cargo Tracking', 'filters': '{"report_type":"Exited Loose Cargo"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-17 11:34:21,782 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'Bond Register Report', 'filters': '{"from_date":"2025-01-01","to_date":"2025-09-17"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-09-17 15:45:23,152 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-09-18 12:25:28,630 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:25:48,667 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:25:48,908 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:28:08,498 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:33:08,568 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:35:11,489 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:35:11,650 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:35:17,854 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:35:18,030 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 12:38:08,515 ERROR frappe New Exception collected in error log
Site: sdg
Form Dict: {}
2025-09-18 14:29:58,914 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:29:59,290 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:00,332 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:01,571 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:01,863 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:16,889 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 14:30:17,125 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {}
2025-09-18 16:19:05,053 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'clearing-charges/CC-CF-2025-0708-00723'}
2025-09-18 16:19:40,076 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'clearing-charges/CC-CF-2025-0708-00723'}
2025-09-18 16:29:25,043 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'app_path': 'clearing-charges/CC-CF-2025-0708-00723'}
